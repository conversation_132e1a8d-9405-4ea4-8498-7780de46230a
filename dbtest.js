require("dotenv").config();
const mysql = require("mysql");

const {
  DB_HOST = "127.0.0.1",
  DB_PORT = 3306,
  DB_USER = "root",
  DB_PASSWORD = "rootpass",
  DB_NAME = "globaldb",
} = process.env;

const connection = mysql.createConnection({
  host: DB_HOST,
  port: DB_PORT,
  user: DB_USER,
  password: DB_PASSWORD,
  database: DB_NAME,
});

connection.connect((err) => {
  if (err) {
    console.error("❌ MySQL bağlantısı başarısız!");
    console.error(err.message);
    process.exit(1);
  } else {
    console.log("✅ MySQL bağlantısı başarılı!");

    connection.query("SELECT 1 + 1 AS result", (err, results) => {
      if (err) {
        console.error("❌ Sorgu hatası:", err.message);
        process.exit(1);
      } else {
        console.log("🔍 Sorgu sonucu:", results[0].result);
        connection.end();
        process.exit(0);
      }
    });
  }
});
