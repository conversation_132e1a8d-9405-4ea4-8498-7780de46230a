{"collectionName": "components_shared_explore", "info": {"displayName": "Explore more by sea", "description": ""}, "options": {}, "attributes": {"destination_guides": {"type": "relation", "relation": "oneToMany", "target": "api::destination.destination"}, "interests": {"type": "relation", "relation": "oneToMany", "target": "api::interest.interest"}, "cruise_lines": {"type": "relation", "relation": "oneToMany", "target": "api::cruise-line.cruise-line"}}}