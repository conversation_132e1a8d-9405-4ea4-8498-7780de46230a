{"collectionName": "components_blocks_call_to_actions", "info": {"displayName": "Call To Actions", "icon": "dashboard", "description": ""}, "options": {}, "attributes": {"heading": {"type": "string"}, "cruise_lines": {"type": "relation", "relation": "oneToMany", "target": "api::cruise-line.cruise-line"}, "description": {"type": "text"}, "sort": {"type": "integer", "default": 0}, "claim_texts": {"type": "component", "repeatable": true, "component": "blocks.text-block"}, "checklist": {"type": "component", "repeatable": true, "component": "shared.button"}}}