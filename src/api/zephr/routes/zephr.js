module.exports = {
  routes: [
    {
      method: "POST",
      path: "/zephr/user-exists",
      handler: "zephr.userExists",
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: "POST",
      path: "/zephr/register",
      handler: "zephr.register",
      config: {
        policies: [],
        middlewares: [],
      },
    },
    {
      method: "POST",
      path: "/zephr/login",
      handler: "zephr.login",
      config: {
        policies: [],
        middlewares: [],
      },
    },
  ],
};
