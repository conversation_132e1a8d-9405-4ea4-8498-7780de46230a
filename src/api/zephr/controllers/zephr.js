"use strict";
const axios = require("axios");

/**
 * A set of functions called "actions" for `zephr`
 * These functions interact with the Zephr API to check user existence,
 * register a new user, and log in an existing user.
 */

module.exports = {
  // Check if a user exists by email address
  userExists: async (ctx) => {
    try {
      const { email_address } = ctx.request.body;

      const response = await axios.post(
        "https://www.cruise-collective.com/zephr/media/user/info",
        {
          identifiers: { email_address },
        },
        {
          headers: { "Content-Type": "application/json" },
        }
      );

      if (response.status === 200 && response.data.exists) {
        ctx.send({ exists: true, data: response.data });
      } else {
        ctx.send({ exists: false, message: "User not found" });
      }
    } catch (error) {
      ctx.send({ error: "An error occurred", details: error.message });
    }
  },

  // Register a new user with an email address and password
  register: async (ctx) => {
    try {
      const { email_address, password } = ctx.request.body;

      const response = await axios.post(
        "https://www.cruise-collective.com/blaize/register",
        {
          identifiers: { email_address },
          validators: {
            password,
            use_sso: false,
          },
          attributes: {}, // Additional attributes can be added here if needed
        },
        {
          headers: { "Content-Type": "application/json" },
        }
      );

      if (response.status === 200) {
        await strapi.plugin("users-permissions").services.user.add({
          username: email, // Assuming the username is the 'name', adjust as necessary
          email: email, // You need to provide an email, adjust as necessary
          password: Math.random().toString(36).slice(-8),
          lastname: surname,
          firstname: name,
          confirmed: true,
          role: 1,
          provider: "local",
        });
        ctx.send({ message: "Registration successful", data: response.data });
      } else {
        ctx.badRequest("Registration failed", response.data);
      }
    } catch (error) {
      ctx.send({
        error: "An error occurred during registration",
        details: error.message,
      });
    }
  },

  // Log in an existing user with an email address and password
  login: async (ctx) => {
    try {
      const { email_address, password } = ctx.request.body;

      const response = await axios.post(
        "https://www.cruise-collective.com/blaize/login",
        {
          identifiers: { email_address },
          validators: { password },
        },
        {
          headers: { "Content-Type": "application/json" },
        }
      );

      if (response.status === 200) {
        const existingUsers = await strapi.entityService.findMany(
          "plugin::users-permissions.user",
          { filters: { email } }
        );

        if (existingUsers.length === 0) {
          await strapi.plugin("users-permissions").services.user.add({
            username: email, // Assuming the username is the 'name', adjust as necessary
            email: email, // You need to provide an email, adjust as necessary
            password: Math.random().toString(36).slice(-8),
            lastname: surname,
            firstname: name,
            confirmed: true,
            role: 1,
            provider: "local",
          });
        }

        ctx.send({ message: "Login successful", data: response.data });
      } else {
        ctx.badRequest("Login failed", response.data);
      }
    } catch (error) {
      ctx.send({
        error: "An error occurred during login",
        details: error.message,
      });
    }
  },
};
