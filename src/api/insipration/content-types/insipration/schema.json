{"kind": "collectionType", "collectionName": "insiprations", "info": {"singularName": "insipration", "pluralName": "insiprations", "displayName": "Articles", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"title": {"type": "string"}, "date": {"type": "date"}, "inspiration_category": {"type": "relation", "relation": "oneToOne", "target": "api::inspiration-category.inspiration-category"}, "user": {"type": "relation", "relation": "oneToOne", "target": "admin::user"}, "featured_image": {"type": "media", "multiple": true, "required": false, "allowedTypes": ["images"]}, "description": {"type": "customField", "options": {"preset": "custom"}, "customField": "plugin::ckeditor5.CKEditor"}, "slug": {"type": "uid", "targetField": "title", "required": true}, "seo": {"type": "component", "repeatable": false, "component": "shared.seo"}, "video": {"type": "customField", "customField": "plugin::video-field.video"}, "excerpt": {"type": "text"}, "explore_more": {"type": "component", "repeatable": false, "component": "blocks.explore-more-by-sea"}, "blocks": {"type": "dynamiczone", "components": ["blocks.gallery", "blocks.call-to-actions", "blocks.split-image", "blocks.annotated-blocks", "blocks.article-cta"]}, "related_articles": {"type": "component", "repeatable": false, "component": "blocks.related-articles"}, "author_info": {"type": "component", "repeatable": false, "component": "shared.author-info"}, "related_article_categories": {"type": "relation", "relation": "oneToMany", "target": "api::inspiration-category.inspiration-category", "mappedBy": "article"}, "partnership": {"type": "component", "repeatable": false, "component": "shared.partnership"}}}