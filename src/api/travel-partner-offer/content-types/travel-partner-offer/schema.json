{"kind": "collectionType", "collectionName": "travel_partner_offers", "info": {"singularName": "travel-partner-offer", "pluralName": "travel-partner-offers", "displayName": "Travel Partner Offer", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"title": {"type": "string", "required": true}, "slug": {"type": "uid", "targetField": "title"}, "description": {"type": "customField", "options": {"preset": "custom"}, "customField": "plugin::ckeditor5.CKEditor"}, "logo": {"type": "media", "multiple": false, "required": false, "allowedTypes": ["images", "files", "videos", "audios"]}, "featured_image": {"type": "media", "multiple": true, "required": false, "allowedTypes": ["images"]}, "expires_date": {"type": "date"}, "offer": {"type": "string"}, "seo": {"type": "component", "repeatable": false, "component": "shared.seo"}, "coupon_code": {"type": "string"}, "affiliate_link": {"type": "string"}, "excerpt": {"type": "text"}, "gallery": {"type": "media", "multiple": true, "required": false, "allowedTypes": ["images"]}, "blocks": {"type": "dynamiczone", "components": ["blocks.call-to-actions", "blocks.gallery", "blocks.split-image", "blocks.annotated-image"]}, "author_info": {"type": "component", "repeatable": false, "component": "shared.author-info"}, "explore_more": {"type": "component", "repeatable": false, "component": "blocks.explore-more-by-sea"}, "related_articles": {"type": "component", "repeatable": false, "component": "blocks.related-articles"}}}