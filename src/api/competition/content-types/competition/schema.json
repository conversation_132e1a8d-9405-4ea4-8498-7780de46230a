{"kind": "collectionType", "collectionName": "competitions", "info": {"singularName": "competition", "pluralName": "competitions", "displayName": "Competition", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"title": {"type": "string", "required": true}, "excerpt": {"type": "customField", "options": {"preset": "custom"}, "customField": "plugin::ckeditor5.CKEditor"}, "featured_image": {"type": "media", "multiple": true, "required": false, "allowedTypes": ["images"]}, "status": {"type": "boolean", "default": false, "required": true}, "open_date": {"type": "date"}, "close_date": {"type": "date"}, "text_editor": {"type": "customField", "options": {"preset": "custom"}, "customField": "plugin::ckeditor5.CKEditor"}, "slug": {"type": "uid", "targetField": "title", "required": true}, "seo": {"type": "component", "repeatable": false, "component": "shared.seo"}, "video": {"type": "customField", "customField": "plugin::video-field.video"}, "gallery": {"type": "media", "multiple": true, "required": false, "allowedTypes": ["images"]}, "blocks": {"type": "dynamiczone", "components": ["blocks.split-image", "blocks.gallery", "blocks.call-to-actions", "blocks.annotated-image"]}, "author_info": {"type": "component", "repeatable": false, "component": "shared.author-info"}, "explore_more": {"type": "component", "repeatable": false, "component": "blocks.explore-more-by-sea"}, "related_articles": {"type": "component", "repeatable": false, "component": "blocks.related-articles"}}}