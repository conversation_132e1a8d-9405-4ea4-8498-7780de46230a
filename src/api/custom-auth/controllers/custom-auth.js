const { sanitize } = require("@strapi/utils");
const axios = require("axios");

module.exports = {
  async customLogin(ctx) {
    const { identifier, password } = ctx.request.body;

    if (!identifier || !password) {
      return ctx.badRequest("Missing identifier or password");
    }

    const isLoginSuccessful = await this.loginZephr(identifier, password);

    if (!isLoginSuccessful) {
      return ctx.badRequest("Invalid identifier or password");
    }

    let user = await strapi.query("plugin::users-permissions.user").findOne({
      where: {
        provider: "local",
        $or: [{ email: identifier.toLowerCase() }, { username: identifier }],
      },
    });

    if (!user) {
      user = await this.createUser(identifier);
    }

    const jwt = this.generateJwt(user.id);
    const sanitizedUser = await this.sanitizeUser(user);

    return ctx.send({ jwt, user: sanitizedUser });
  },

  async createUser(identifier) {
    const userData = {
      username: identifier,
      email: identifier,
      password: Math.random().toString(36).slice(-8),
      lastname: "",
      firstname: "",
      confirmed: true,
      role: 1,
      provider: "local",
    };

    return await strapi.plugin("users-permissions").services.user.add(userData);
  },

  generateJwt(userId) {
    return strapi
      .service("plugin::users-permissions.jwt")
      .issue({ id: userId });
  },

  async sanitizeUser(user) {
    return await sanitize.contentAPI.output(
      user,
      strapi.getModel("plugin::users-permissions.user")
    );
  },

  async userExist(email_address) {
    try {
      const response = await axios.post(
        "https://www.cruise-collective.com/zephr/media/user/info",
        { identifiers: { email_address } },
        { headers: { "Content-Type": "application/json" } }
      );

      return response.status === 200;
    } catch (error) {
      return false;
    }
  },

  async loginZephr(email_address, password) {
    try {
      const response = await axios.post(
        "https://www.cruise-collective.com/blaize/login",
        {
          identifiers: { email_address },
          validators: { password },
        },
        { headers: { "Content-Type": "application/json" } }
      );

      return response.status === 200;
    } catch (error) {
      return false;
    }
  },
};
