{"kind": "collectionType", "collectionName": "cookie_popups", "info": {"singularName": "cookie-popup", "pluralName": "cookie-popups", "displayName": "<PERSON><PERSON>"}, "options": {"draftAndPublish": false}, "pluginOptions": {"i18n": {"localized": true}, "content-manager": {"visible": false}}, "attributes": {"title": {"pluginOptions": {"i18n": {"localized": true}}, "type": "string"}, "description": {"pluginOptions": {"i18n": {"localized": true}}, "type": "richtext"}, "buttons": {"type": "component", "repeatable": true, "pluginOptions": {"i18n": {"localized": true}}, "component": "shared.cookie-button"}, "hasCustomizability": {"pluginOptions": {"i18n": {"localized": true}}, "type": "boolean", "default": false, "required": true}}}