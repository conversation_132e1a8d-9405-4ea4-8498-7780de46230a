{"kind": "singleType", "collectionName": "homepages", "info": {"singularName": "homepage", "pluralName": "homepages", "displayName": "Homepage", "description": ""}, "options": {"draftAndPublish": false}, "pluginOptions": {}, "attributes": {"sliders": {"type": "component", "repeatable": true, "component": "blocks.sliders"}, "seo": {"type": "component", "repeatable": false, "component": "shared.seo"}, "articles": {"type": "relation", "relation": "oneToMany", "target": "api::insipration.insipration"}, "category_promo": {"type": "component", "repeatable": true, "component": "homepage.category-promo"}, "promos": {"type": "component", "repeatable": true, "component": "blocks.promo"}, "cta": {"type": "component", "repeatable": false, "component": "blocks.call-to-actions"}, "saving_block_promos": {"type": "component", "repeatable": true, "component": "blocks.promo"}, "cta2": {"type": "component", "repeatable": false, "component": "blocks.call-to-actions"}, "countdown": {"type": "component", "repeatable": false, "component": "homepage.countdown"}}}