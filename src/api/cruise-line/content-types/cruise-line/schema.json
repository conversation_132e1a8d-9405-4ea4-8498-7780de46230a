{"kind": "collectionType", "collectionName": "cruise_lines", "info": {"singularName": "cruise-line", "pluralName": "cruise-lines", "displayName": "Cruise Line", "description": ""}, "options": {"draftAndPublish": true}, "pluginOptions": {}, "attributes": {"featured_image": {"type": "media", "multiple": true, "required": false, "allowedTypes": ["images"]}, "title": {"type": "string"}, "logo": {"type": "media", "multiple": false, "required": false, "allowedTypes": ["images"]}, "slug": {"type": "uid", "targetField": "title", "required": true}, "excerpt": {"type": "richtext"}, "seo": {"type": "component", "repeatable": false, "component": "shared.seo"}, "video": {"type": "customField", "customField": "plugin::video-field.video"}, "offer": {"type": "string"}, "affiliate_link": {"type": "string"}, "calloutbox": {"type": "component", "repeatable": false, "component": "blocks.callout"}, "blocks": {"type": "dynamiczone", "components": ["blocks.call-to-actions", "blocks.explore-more-by-sea", "blocks.info-box-slider", "blocks.promo", "blocks.testimonial", "blocks.fancy-image-section", "blocks.related-articles", "blocks.text-block"]}, "headline": {"type": "string"}, "extra_page": {"type": "component", "repeatable": false, "component": "cruise-line.extra-pages"}}}