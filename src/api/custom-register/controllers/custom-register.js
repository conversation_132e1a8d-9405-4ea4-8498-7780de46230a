const { sanitize } = require("@strapi/utils");
const axios = require("axios");
module.exports = {
  async customRegister(ctx) {
    const { email, password, firstname, lastname, newsletter } =
      ctx.request.body;

    // Validate the input
    if (!email || !password) {
      return ctx.badRequest("Missing email, password or username");
    }

    const registreZephr = await this.registerZephr(email, password, newsletter,firstname,lastname);

    if (!registreZephr) {
      return ctx.badRequest("The user could not be created");
    }

    const user = await strapi.service("plugin::users-permissions.user").add({
      email: email.toLowerCase(),
      username: email.toLowerCase(),
      password,
      lastname,
      firstname,
      role: 1,
      provider: "local",
      confirmed: true, // Automatically confirm the user (adjust this as needed)
    });

    // Generate JWT token
    const jwt = strapi
      .service("plugin::users-permissions.jwt")
      .issue({ id: user.id });

    // Sanitize the user before sending it to the client
    const sanitizedUser = await sanitize.contentAPI.output(
      user,
      strapi.getModel("plugin::users-permissions.user")
    );

    // If newsletter is true, send email to Zapier webhook
    if (newsletter === true) {
      await this.sendNewsletterSignup(email);
    }

    return ctx.send({
      jwt,
      user: sanitizedUser,
    });
  },
  async registerZephr(email_address, password, newsletter,firstname,lastname) {
    const isNewsletter = newsletter ? "Y" : "N";
    try {
      const response = await axios.post(
        "https://www.cruise-collective.com/blaize/register",
        {
          identifiers: { email_address },
          validators: { password, use_sso: false },
          attributes: {
            "cruise-collective-newsletter2": isNewsletter,
            "first-name": firstname,
            "last-name": lastname,
          },
        },
        { headers: { "Content-Type": "application/json" } }
      );

      return response.status === 200;
    } catch (error) {
      console.error(error);
      return false;
    }
  },
  async sendNewsletterSignup(email) {
    try {
      const response = await axios.post(
        "https://hooks.zapier.com/hooks/catch/15997012/2hql4qt/",
        {
          email: email,
        },
        { headers: { "Content-Type": "application/json" } }
      );

      if (response.status === 200) {
        console.log("Newsletter signup successful!");
      } else {
        console.log("Failed to sign up for newsletter.");
      }
    } catch (error) {
      console.error("Error signing up for newsletter:", error);
    }
  },
};
