const axios = require("axios");
const { sanitize } = require("@strapi/utils");

module.exports = {
  async googleAuth(ctx) {
    const { id_token } = ctx.query;

    if (!id_token) {
      return ctx.badRequest("ID token is required.");
    }

    // Step 1: Verify the Google ID token using Google's API
    let googleUser;
    try {
      const response = await axios.get(
        `https://oauth2.googleapis.com/tokeninfo?id_token=${id_token}`
      );
      googleUser = response.data;
    } catch (err) {
      return ctx.badRequest("Invalid Google ID token.");
    }

    const { email, sub: googleId, name } = googleUser;

    // Step 2: Sync with Zephr - Check if the user exists or register the user in Zephr
    const isZephrSynced = await this.syncWithZephr(email);

    if (!isZephrSynced) {
      return ctx.badRequest("Could not sync user with <PERSON><PERSON><PERSON>.");
    }

    // Step 3: Check if the user already exists in Strapi
    let user = await strapi.query("plugin::users-permissions.user").findOne({
      where: { email: email.toLowerCase() },
    });

    // Step 4: If the user doesn't exist in Strapi, create a new user
    if (!user) {
      user = await strapi.service("plugin::users-permissions.user").add({
        email: email.toLowerCase(),
        username: email.toLowerCase(),
        lastname: "",
        firstname: "",
        role: 1,
        provider: "google",
        password: googleId, // You can use the Google ID as the password or generate a random one
        confirmed: true, // Automatically confirm the user
      });
    }

    // Step 5: Generate a JWT token for the user
    const jwt = strapi
      .service("plugin::users-permissions.jwt")
      .issue({ id: user.id });

    // Step 6: Sanitize the user object before sending it to the client
    const sanitizedUser = await sanitize.contentAPI.output(
      user,
      strapi.getModel("plugin::users-permissions.user")
    );

    await this.sendNewsletterSignup(email);

    return ctx.send({
      jwt,
      user: sanitizedUser,
    });
  },

  // Function to sync user with Zephr - either verify or register the user
  async syncWithZephr(email) {
    try {
      // Check if user exists in Zephr
      const userExists = await this.userExistsInZephr(email);
      console.log(`User ${email} exists in Zephr: ${userExists}`);
      if (!userExists) {
        // If the user doesn't exist, register the user in Zephr
        const registerZephr = await this.registerUserInZephr(email);
        if (!registerZephr) {
          throw new Error("Failed to register user in Zephr.");
        }
      }
      return true;
    } catch (error) {
      console.error("Zephr sync error:", error);
      return false;
    }
  },

  // Function to check if user exists in Zephr
  async userExistsInZephr(email) {
    try {
      const response = await axios.post(
        "https://www.cruise-collective.com/zephr/media/user/info",
        { identifiers: { email_address: email } },
        { headers: { "Content-Type": "application/json" } }
      );
      return response.status === 200;
    } catch (error) {
      console.error("Error checking Zephr user existence:", error);
      return false;
    }
  },

  // Function to register the user in Zephr
  async registerUserInZephr(email) {
    try {
      const response = await axios.post(
        "https://www.cruise-collective.com/blaize/register",
        {
          identifiers: { email_address: email },
          validators: {
            password: this.generatePassword(),
            use_sso: false,
          },
          attributes: {
            "cruise-collective-newsletter2": "Y",
          },
        },
        { headers: { "Content-Type": "application/json" } }
      );
      return response.status === 200;
    } catch (error) {
      console.error("Error registering Zephr user:", error);
      return false;
    }
  },
  generatePassword(length = 8) {
    const uppercase = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
    const lowercase = "abcdefghijklmnopqrstuvwxyz";
    const numbers = "0123456789";
    const specialChars = "!@#$%^&*()_+{}[]|:;<>,.?/~";

    const getRandomChar = (chars) =>
      chars[Math.floor(Math.random() * chars.length)];

    // Ensure at least one character from each required set
    let password = [
      getRandomChar(uppercase),
      getRandomChar(numbers),
      getRandomChar(specialChars),
    ];

    // Fill the remaining characters randomly from all sets
    const allChars = uppercase + lowercase + numbers + specialChars;
    while (password.length < length) {
      password.push(getRandomChar(allChars));
    }

    // Shuffle the password to avoid predictable patterns
    return password.sort(() => Math.random() - 0.5).join("");
  },
  async sendNewsletterSignup(email) {
    try {
      const response = await axios.post(
        "https://hooks.zapier.com/hooks/catch/15997012/2hql4qt/",
        {
          email: email,
        },
        { headers: { "Content-Type": "application/json" } }
      );

      if (response.status === 200) {
        console.log("Newsletter signup successful!");
      } else {
        console.log("Failed to sign up for newsletter.");
      }
    } catch (error) {
      console.error("Error signing up for newsletter:", error);
    }
  },
};
