globalThis.CKEditorConfig = {

    /* By default custom configs and theme
    defined in this file are going to be spread into
    default configs and theme these two properties below
    allow you to redefine default objects completely: */

    //configsOverwrite:true,
    //themeOverwrite:true,

    /* Here you can redefine default configs
    or add completely new ones.
    Each config includes:
    "styles", "field" and "editorConfig" properties.
    Property "field" is required. */

    configs:{
        toolbar:{
            // styles:``,
            // field:{},
            // editorConfig:{}
        },
        custom:{

            /* Styles for this specific editor version.
            This will be passed into the editor's parent container. */

            styles:`
            //     --ck-focus-ring:3px dashed #5CB176;

            //     .ck.ck-content.ck-editor__editable {
            //       &.ck-focused:not(.ck-editor__nested-editable) {
            //         border: var(--ck-focus-ring) !important;
            //       }
            //     }
                 .ck.ck-content.ck-editor__editable.ck-rounded-corners.ck-editor__editable_inline.ck-blurred{
                   max-height: 4000px;
                 }
                 .ck.ck-content.ck-editor__editable.ck-rounded-corners.ck-editor__editable_inline.ck-focused{
                   min-height: 400px;
                   max-height: 4000px;
                 }
            `,

            /* Custom field option */
            field: {
                key: "custom",
                value: "custom",
                metadatas: {
                  intlLabel: {
                    id: "ckeditor5.preset.custom.label",
                    defaultMessage: "Custom version",
                  },
                },
            },
            /* CKEditor configuration */
            editorConfig:{
                /* You can find all available built-in plugins
                in the admin/src/components/Input/CKEditor/configs/base.js */
                plugins: [
                    CKEditor5.autoformat.Autoformat,
                    CKEditor5.basicStyles.Bold,
                    CKEditor5.basicStyles.Italic,
                    CKEditor5.basicStyles.Underline,
                    CKEditor5.essentials.Essentials,
                    CKEditor5.heading.Heading,
                    CKEditor5.image.AutoImage,
                    CKEditor5.image.Image,
                    CKEditor5.image.ImageCaption,
                    CKEditor5.image.ImageInsert,
                    CKEditor5.image.ImageResize,
                    CKEditor5.image.ImageStyle,
                    CKEditor5.image.ImageToolbar,
                    CKEditor5.image.ImageUpload,
                    CKEditor5.image.PictureEditing,
                    CKEditor5.mediaEmbed.MediaEmbed,
                    CKEditor5.mediaEmbed.MediaEmbedToolbar,
                    CKEditor5.htmlEmbed.HtmlEmbed,
                    CKEditor5.indent.Indent,
                    CKEditor5.link.Link,
                    CKEditor5.link.AutoLink,
                    CKEditor5.link.LinkImage,
                    CKEditor5.list.List,
                    CKEditor5.pageBreak.PageBreak,
                    CKEditor5.paragraph.Paragraph,
                    CKEditor5.pasteFromOffice.PasteFromOffice,
                    CKEditor5.table.Table,
                    CKEditor5.table.TableToolbar,
                    CKEditor5.table.TableColumnResize,
                    CKEditor5.table.TableCaption,
                    CKEditor5.table.TableProperties,
                    CKEditor5.table.TableCellProperties,
                    CKEditor5.strapiPlugins.StrapiMediaLib,
                    CKEditor5.strapiPlugins.StrapiUploadAdapter,
                    CKEditor5.font.FontSize,
                    CKEditor5.font.FontColor,
                    CKEditor5.font.FontFamily,
                    CKEditor5.basicStyles.Subscript,
                    CKEditor5.basicStyles.Superscript,
                    CKEditor5.basicStyles.Strikethrough,
                    CKEditor5.horizontalLine.HorizontalLine,
                    CKEditor5.list.TodoList,
                    CKEditor5.alignment.Alignment,
                    CKEditor5.indent.Indent,
                    CKEditor5.removeFormat.RemoveFormat,
                    CKEditor5.basicStyles.Code,
                    CKEditor5.codeBlock.CodeBlock,
                    CKEditor5.blockQuote.BlockQuote,
                    CKEditor5.specialCharacters.SpecialCharacters,
                    CKEditor5.specialCharacters.SpecialCharactersArrows,
                    CKEditor5.specialCharacters.SpecialCharactersCurrency,
                    CKEditor5.specialCharacters.SpecialCharactersEssentials,
                    CKEditor5.specialCharacters.SpecialCharactersLatin,
                    CKEditor5.specialCharacters.SpecialCharactersMathematical,
                    CKEditor5.specialCharacters.SpecialCharactersText,
                  ],

                  /* By default, the language of the plugin's UI will be chosen
                  based on the language defined in this config file
                  or on the preferred language from the strapi's user config
                  and if both of them are not set then 'en' will be used as a fallback.
                  ( language.ui -> preferred language -> 'en' ) */

                  /* For content it will chose the language based on i18n (if! ignorei18n)
                  or on language.content property defined here
                  and it will use UI language as a fallback.
                  ignorei18n ? language.content : i18n; -> language.ui */

                  language:{
                    // ignorei18n: true,
                    // ui:'he',
                    // content:'he'
                  },
                toolbar: [
                        'heading',
                        'fontSize',
                        'fontFamily',
                        'fontColor',
                        '|',
                        'bold',
                        'italic',
                        'underline',
                        'subscript',
                        'superscript',
                        'strikethrough',
                        'horizontalLine',
                        '|',
                        'link',
                        'strapiMediaLib',
                        'bulletedList',
                        'numberedList',
                        'todoList',
                        '|',
                        'alignment',
                        'indent',
                        'outdent',
                        '|',
                        'insertTable',
                        'removeFormat',
                        'blockQuote',
                        'code',
                        'insertImage','mediaEmbed','specialCharacters','htmlEmbed','codeBlock',
                        '|',
                        'fullScreen',
                        'undo',
                        'redo',
                  ],

                  heading: {
                    options: [
                      { model: 'paragraph', title: 'Paragraph', class: 'ck-heading_paragraph' },
                      { model: 'heading1', view: 'h1', title: 'Heading 1', class: 'ck-heading_heading1' },
                      { model: 'heading2', view: 'h2', title: 'Heading 2', class: 'ck-heading_heading2' },
                      { model: 'heading3', view: 'h3', title: 'Heading 3', class: 'ck-heading_heading3' },
                      { model: 'heading4', view: 'h4', title: 'Heading 4', class: 'ck-heading_heading4' },
                      { model: "heading5", view: "h5", title: "Heading 5", class: "ck-heading_heading5" },
                      { model: "heading6", view: "h6", title: "Heading 6", class: "ck-heading_heading6" },
                    ]
                  },
                    fontSize: {
                        options: [10, 12, 14, "default", 18, 20, 22, 24, 26, 28, 30, 32, 36, 38],
                        supportAllValues: true,
                    },
                   fontFamily: {
                      options: [
                          '"adobe-garamond-pro", serif',
                          'ApercuRegular',
                          'ApercuMedium',
                          'ApercuBold',
                          'ApercuProRegular',
                          'ApercuProMedium',
                      ],
                       supportAllValues: true,
                  },
               image: {
                      styles: {
                        options: [
                          'inline', 'alignLeft', 'alignRight',
                          'alignCenter', 'alignBlockLeft', 'alignBlockRight',
                          'block', 'side'
                        ]
                      },
                      resizeOptions: [
                        {
                          name: "resizeImage:original",
                          label: "Default image width",
                          value: null,
                        },
                        {
                          name: "resizeImage:50",
                          label: "50% page width",
                          value: "50",
                        },
                        {
                          name: "resizeImage:75",
                          label: "75% page width",
                          value: "75",
                        },
                      ],
                      toolbar: [
                        "imageTextAlternative",
                        "toggleImageCaption",
                        "linkImage",
                        "|",
                        "imageStyle:inline",
                        "imageStyle:wrapText",
                        "imageStyle:breakText",
                        "imageStyle:side",
                        "|",
                        "resizeImage",
                      ],
                      insert: {
                        integrations: ["insertImageViaUrl"],
                      },
                  },
                  table: {
                    contentToolbar: [
                      'tableColumn',
                      'tableRow',
                      'mergeTableCells',
                      '|',
                      'toggleTableCaption'
                    ]
                  },
                    link: {
                    decorators: {
                      openInNewTab: {
                        mode: "manual",
                        label: "Open in a new tab",
                        defaultValue: true,
                        attributes: {
                          target: "_blank",
                          rel: "noopener noreferrer",
                        },
                      },
                      toggleDownloadable: {
                        mode: "manual",
                        label: "Downloadable",
                        attributes: {
                          download: "file",
                        },
                      },
                    },
                    addTargetToExternalLinks: true,
                    defaultProtocol: "https://",
                  },
            }
        }
    },

    /* Here you can customize the plugin's theme.
    This will be passed as "createGlobalStyle". */
    theme:{
        // common:``,
        // light:``,
        // dark:``,
        // additional:``
    }
}
