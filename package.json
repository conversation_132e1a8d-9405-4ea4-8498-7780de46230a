{"name": "backend", "version": "0.1.0", "private": true, "description": "A Strapi application", "license": "MIT", "author": {"name": "A Strapi developer"}, "scripts": {"build": "strapi build", "develop": "strapi develop", "import-users": "node tools/import.js", "sitemap": "strapi-sitemap", "start": "strapi start", "strapi": "strapi"}, "dependencies": {"@_sh/strapi-plugin-ckeditor": "^2.1.3", "@ckeditor/ckeditor5-markdown-gfm": "^43.1.0", "@ckeditor/strapi-plugin-ckeditor": "^0.0.13", "@sklinet/strapi-plugin-video-field": "^1.0.6", "@strapi/plugin-color-picker": "^4.25.10", "@strapi/plugin-i18n": "^4.25.10", "@strapi/plugin-seo": "^1.9.9", "@strapi/plugin-users-permissions": "^4.25.10", "@strapi/provider-email-mailgun": "^4.25.10", "@strapi/provider-email-sendgrid": "^4.25.10", "@strapi/provider-upload-aws-s3": "^4.25.10", "@strapi/strapi": "4.25.10", "axios": "^1.7.7", "csv-parser": "^3.0.0", "mysql": "^2.18.1", "node-schedule": "^2.1.1", "react": "^18.0.0", "react-dom": "^18.0.0", "react-router-dom": "^5.2.0", "strapi-plugin-config-sync": "^1.2.6", "strapi-plugin-cookie-manager": "^1.2.4", "strapi-plugin-country-select": "^1.0.3", "strapi-plugin-multi-select": "^1.2.3", "strapi-plugin-navigation": "^2.5.4", "strapi-plugin-populate-deep": "^3.0.1", "strapi-plugin-preview-button": "^2.2.2", "strapi-plugin-rest-cache": "^4.2.9", "strapi-plugin-sitemap": "^3.2.0", "styled-components": "^5.2.1"}, "devDependencies": {}, "engines": {"node": ">=14.19.1 <=20.11.1", "npm": ">=6.0.0"}, "strapi": {"uuid": "************************************"}}